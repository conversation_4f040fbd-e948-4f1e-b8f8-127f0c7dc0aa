<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="./output.css" rel="stylesheet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Arvo:ital,wght@0,400;0,700;1,400;1,700&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap');
    body::-webkit-scrollbar {
      display: none;
    }
    body {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    .font-arvo {
      font-family: 'Arvo', serif;
    }

    /* Professional Animated Background */
    .animated-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 30%, #9ca3af 70%, #6b7280 100%);
      overflow: hidden;
      z-index: 0;
    }

    /* Horizontal Animated Lines */
    .h-line {
      position: absolute;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.7), transparent);
      animation: moveHorizontal 5s linear infinite;
      will-change: transform, opacity;
    }

    .h-line:nth-child(1) {
      width: 250px;
      top: 15%;
      left: -250px;
      animation-delay: 0s;
      animation-duration: 6s;
    }

    .h-line:nth-child(2) {
      width: 180px;
      top: 35%;
      left: -180px;
      animation-delay: 1.5s;
      animation-duration: 4.5s;
      background: linear-gradient(90deg, transparent, rgba(15, 23, 42, 0.8), transparent);
    }

    .h-line:nth-child(3) {
      width: 320px;
      top: 55%;
      left: -320px;
      animation-delay: 3s;
      animation-duration: 7s;
      background: linear-gradient(90deg, transparent, rgba(29, 78, 216, 0.6), transparent);
    }

    .h-line:nth-child(4) {
      width: 200px;
      top: 75%;
      left: -200px;
      animation-delay: 4.5s;
      animation-duration: 5.5s;
      background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.65), transparent);
    }

    .h-line:nth-child(5) {
      width: 280px;
      top: 25%;
      left: -280px;
      animation-delay: 2s;
      animation-duration: 6.5s;
      background: linear-gradient(90deg, transparent, rgba(15, 23, 42, 0.75), transparent);
    }

    .h-line:nth-child(6) {
      width: 150px;
      top: 65%;
      left: -150px;
      animation-delay: 5s;
      animation-duration: 4s;
      background: linear-gradient(90deg, transparent, rgba(29, 78, 216, 0.7), transparent);
    }

    /* Vertical Animated Lines */
    .v-line {
      position: absolute;
      width: 1px;
      background: linear-gradient(180deg, transparent, rgba(30, 58, 138, 0.6), transparent);
      animation: moveVertical 6s linear infinite;
      will-change: transform, opacity;
    }

    .v-line:nth-child(7) {
      height: 200px;
      left: 15%;
      top: -200px;
      animation-delay: 1s;
      animation-duration: 7s;
    }

    .v-line:nth-child(8) {
      height: 150px;
      left: 45%;
      top: -150px;
      animation-delay: 3.5s;
      animation-duration: 5s;
      background: linear-gradient(180deg, transparent, rgba(15, 23, 42, 0.7), transparent);
    }

    .v-line:nth-child(9) {
      height: 180px;
      left: 75%;
      top: -180px;
      animation-delay: 2.5s;
      animation-duration: 6.5s;
      background: linear-gradient(180deg, transparent, rgba(29, 78, 216, 0.65), transparent);
    }

    .v-line:nth-child(10) {
      height: 220px;
      left: 85%;
      top: -220px;
      animation-delay: 4s;
      animation-duration: 5.5s;
      background: linear-gradient(180deg, transparent, rgba(30, 58, 138, 0.8), transparent);
    }

    /* Diagonal Lines for Added Sophistication */
    .d-line {
      position: absolute;
      width: 1px;
      height: 150px;
      background: linear-gradient(180deg, transparent, rgba(15, 23, 42, 0.5), transparent);
      animation: moveDiagonal 8s linear infinite;
      transform-origin: top;
      will-change: transform, opacity;
    }

    .d-line:nth-child(11) {
      left: 25%;
      top: -150px;
      transform: rotate(45deg);
      animation-delay: 1.5s;
    }

    .d-line:nth-child(12) {
      left: 65%;
      top: -150px;
      transform: rotate(-45deg);
      animation-delay: 4s;
      background: linear-gradient(180deg, transparent, rgba(29, 78, 216, 0.4), transparent);
    }

    /* Animation Keyframes */
    @keyframes moveHorizontal {
      0% {
        transform: translateX(0);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateX(calc(100vw + 350px));
        opacity: 0;
      }
    }

    @keyframes moveVertical {
      0% {
        transform: translateY(0);
        opacity: 0;
      }
      15% {
        opacity: 1;
      }
      85% {
        opacity: 1;
      }
      100% {
        transform: translateY(calc(100vh + 250px));
        opacity: 0;
      }
    }

    @keyframes moveDiagonal {
      0% {
        transform: translateY(0) rotate(45deg);
        opacity: 0;
      }
      20% {
        opacity: 1;
      }
      80% {
        opacity: 1;
      }
      100% {
        transform: translateY(calc(100vh + 200px)) rotate(45deg);
        opacity: 0;
      }
    }

  </style>
</head>
<body class=" text-gray-800 font-sans">

<div class="relative h-screen overflow-hidden">
   

    <div class="relative z-10 h-full flex flex-col">
        <!-- Navbar -->
        <header class="bg-white bg-opacity-20 fixed w-full z-50 top-0">
            <div class="max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
                <img src="./assests/nobg.png" alt="Vishnorex Logo" class="h-12">
                <nav class="space-x-6 hidden md:flex">
                    <a href="#about" class="text-blue-800 font-arvo hover:text-blue-500">About</a>
                    <a href="#services" class="text-blue-800 font-arvo hover:text-blue-500">Services</a>
                    <a href="#projects" class="text-blue-800 font-arvo hover:text-blue-500">Projects</a>
                    <a href="#contact" class="text-blue-800 font-arvo hover:text-blue-500">Contact</a>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="flex-grow flex items-center justify-center text-center">
            <div class="max-w-4xl mx-auto px-6">
                <h2 class="text-4xl md:text-5xl font-extrabold text-gradient mb-6">We Build Digital Dreams</h2>
                <p class="text-lg text-blue-950 mb-8">Mobile-first solutions with precision, performance, and purpose.</p>
                <a href="#contact" class="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition">Start Your Project</a>
            </div>
        </section>
    </div>
</div>

  <!-- About Section -->
  <section id="about" class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-4">About Vishnorex</h3>
      <p class="text-gray-600 text-lg">We specialize in mobile app development, web design, and branding. Our mission is to turn bold ideas into powerful digital experiences.</p>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="py-20 bg-gray-100">
    <div class="max-w-6xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-12">Our Services</h3>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Web Development</h4>
          <p class="text-gray-600">Responsive websites using HTML, Tailwind CSS, and JavaScript.</p>
        </div>
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Mobile App Design</h4>
          <p class="text-gray-600">Intuitive UI/UX for Android and iOS platforms.</p>
        </div>
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Brand Identity</h4>
          <p class="text-gray-600">Professional seals, logos, and visual branding.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Projects Section -->
  <section id="projects" class="py-20 bg-white">
    <div class="max-w-6xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-12">Featured Projects</h3>
      <div class="grid md:grid-cols-2 gap-8">
        <div class="bg-gray-100 p-6 rounded-lg shadow">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Travel Management System</h4>
          <p class="text-gray-600">Smart route planning and booking dashboard.</p>
        </div>
        <div class="bg-gray-100 p-6 rounded-lg shadow">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Hospital Monitoring Dashboard</h4>
          <p class="text-gray-600">Real-time health tracking with backend integration.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="py-20 bg-indigo-100">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-700 mb-6">Let’s Connect</h3>
      <form id="contactForm" class="space-y-4">
        <input type="text" placeholder="Your Name" class="w-full p-3 rounded border border-gray-300" required />
        <input type="email" placeholder="Your Email" class="w-full p-3 rounded border border-gray-300" required />
        <textarea placeholder="Your Message" class="w-full p-3 rounded border border-gray-300" rows="4" required></textarea>
        <button type="submit" class="bg-indigo-600 text-white px-6 py-3 rounded hover:bg-indigo-700 transition">Send Message</button>
      </form>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-white py-6 text-center border-t">
    <p class="text-sm text-gray-500">© 2025 Vishnorex Technologies Private Limited. All rights reserved.</p>
  </footer>

  <!-- JavaScript -->
  <script>
    document.getElementById("contactForm").addEventListener("submit", function(e) {
      e.preventDefault();
      alert("Thanks for contacting Vishnorex! We'll get back to you soon.");
    });
  </script>

</body>
</html>
