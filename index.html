<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies - Custom Software Solutions</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="./output.css" rel="stylesheet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body::-webkit-scrollbar {
      display: none;
    }
    body {
      -ms-overflow-style: none;
      scrollbar-width: none;
      font-family: 'Inter', sans-serif;
    }
    
    .font-space {
      font-family: 'Space Grotesk', sans-serif;
    }

    /* Modern Tech Company Design */
    .hero-section {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
      min-height: 100vh;
      position: relative;
      overflow: hidden;
    }
    
    .hero-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      opacity: 0.6;
    }

    /* Modern Navigation */
    .modern-nav {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }
    
    /* Tech Cards */
    .tech-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(59, 130, 246, 0.1);
    }
    
    .tech-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.2);
    }

    /* Modern Buttons */
    .btn-primary {
      background: linear-gradient(135deg, #3b82f6, #1e40af);
      color: white;
      padding: 14px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      background: linear-gradient(135deg, #1e40af, #1e3a8a);
    }
    
    .btn-secondary {
      background: white;
      color: #1e40af;
      padding: 14px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: 2px solid #3b82f6;
    }
    
    .btn-secondary:hover {
      background: #3b82f6;
      color: white;
      transform: translateY(-2px);
    }

    /* Text Styles */
    .text-gradient {
      background: linear-gradient(135deg, #1e40af, #3b82f6, #60a5fa);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    /* Section Styles */
    .section-light {
      background: #f8fafc;
    }
    
    .section-white {
      background: white;
    }
    
    .section-blue {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
      color: white;
    }
    
    /* Stats Counter */
    .stat-number {
      font-size: 3rem;
      font-weight: 800;
      color: #1e40af;
      line-height: 1;
    }
    
    /* Feature Icons */
    .feature-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #3b82f6, #60a5fa);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-bottom: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero-section {
        padding: 2rem 1rem;
      }
      
      .stat-number {
        font-size: 2rem;
      }
      
      .tech-card {
        margin-bottom: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- Modern Tech Company Website -->
  
  <!-- Navigation -->
  <nav class="modern-nav fixed w-full z-50 top-0">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10">
          <span class="font-space font-bold text-xl text-gray-800">Vishnorex</span>
        </div>
        <div class="hidden md:flex space-x-8">
          <a href="#home" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Home</a>
          <a href="#about" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">About</a>
          <a href="#services" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Services</a>
          <a href="#portfolio" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Portfolio</a>
          <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Contact</a>
        </div>
        <a href="#contact" class="btn-primary hidden md:inline-block">Get Started</a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-pattern"></div>
    <div class="relative z-10 flex items-center justify-center min-h-screen px-6">
      <div class="text-center max-w-5xl mx-auto">
        <h1 class="text-5xl md:text-7xl font-space font-bold text-white mb-6 leading-tight">
          Custom Software Solutions to help Your Business 
          <span class="text-blue-200">Succeed</span>
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 mb-10 leading-relaxed max-w-3xl mx-auto">
          We transform innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="#contact" class="btn-primary text-lg px-8 py-4">Start Your Project</a>
          <a href="#about" class="btn-secondary text-lg px-8 py-4">Learn More</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="section-white py-20">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div>
          <div class="stat-number">50+</div>
          <p class="text-gray-600 font-medium">Projects Completed</p>
        </div>
        <div>
          <div class="stat-number">25+</div>
          <p class="text-gray-600 font-medium">Happy Clients</p>
        </div>
        <div>
          <div class="stat-number">3+</div>
          <p class="text-gray-600 font-medium">Years Experience</p>
        </div>
        <div>
          <div class="stat-number">24/7</div>
          <p class="text-gray-600 font-medium">Support</p>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="section-light py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid md:grid-cols-2 gap-16 items-center">
        <div>
          <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">
            About Vishnorex Technologies
          </h2>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            We are a forward-thinking software development company specializing in custom solutions that drive business growth. Our team combines technical expertise with creative innovation to deliver exceptional digital experiences.
          </p>
          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">Custom Software Development</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">Mobile App Development</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">AI & Machine Learning Solutions</span>
            </div>
          </div>
        </div>
        <div class="relative">
          <div class="tech-card p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">Our Mission</h3>
            <p class="text-gray-600 leading-relaxed">
              To empower businesses with innovative technology solutions that transform ideas into reality and drive sustainable growth in the digital age.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="section-blue py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-white mb-6">Our Services</h2>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto">
          We offer comprehensive technology solutions to help your business thrive in the digital landscape.
        </p>
      </div>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20">
          <div class="feature-icon">
            <span>🌐</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">Web Development</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Modern, responsive web applications built with React, Next.js, and cutting-edge frameworks for optimal performance.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">React</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">Next.js</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">TypeScript</span>
          </div>
        </div>
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20">
          <div class="feature-icon">
            <span>📱</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">Mobile Apps</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Native iOS and Android applications with Flutter and React Native for seamless cross-platform experiences.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">Flutter</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">React Native</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">Swift</span>
          </div>
        </div>
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20">
          <div class="feature-icon">
            <span>🤖</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">AI Solutions</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Intelligent solutions powered by machine learning, data analytics, and artificial intelligence technologies.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">TensorFlow</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">PyTorch</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full">OpenAI</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="section-light py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">Featured Projects</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Discover some of our recent work and see how we've helped businesses transform their digital presence.
        </p>
      </div>
      <div class="grid md:grid-cols-2 gap-8">
        <div class="tech-card p-8">
          <div class="text-5xl mb-6">🚀</div>
          <h3 class="text-2xl font-bold text-gray-800 mb-4">AI-Powered Travel Platform</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Intelligent route optimization with machine learning algorithms, real-time booking, and personalized recommendations for enhanced user experience.
          </p>
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">React</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Node.js</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">AI/ML</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">MongoDB</span>
          </div>
          <a href="#" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors">View Project →</a>
        </div>
        <div class="tech-card p-8">
          <div class="text-5xl mb-6">🏥</div>
          <h3 class="text-2xl font-bold text-gray-800 mb-4">Smart Healthcare Dashboard</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Real-time patient monitoring with IoT integration, predictive analytics, and automated alert systems for improved healthcare delivery.
          </p>
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Vue.js</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Python</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">IoT</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">PostgreSQL</span>
          </div>
          <a href="#" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors">View Project →</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="section-white py-24">
    <div class="max-w-4xl mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">Let's Build Something Amazing Together</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Ready to transform your ideas into reality? We'd love to hear about your project and discuss how we can help you succeed.
        </p>
      </div>
      <div class="tech-card p-12">
        <form class="space-y-6">
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <label class="block text-gray-700 font-medium mb-2">Your Name</label>
              <input type="text" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="John Doe" required>
            </div>
            <div>
              <label class="block text-gray-700 font-medium mb-2">Email Address</label>
              <input type="email" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="<EMAIL>" required>
            </div>
          </div>
          <div>
            <label class="block text-gray-700 font-medium mb-2">Project Type</label>
            <select class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
              <option>Web Development</option>
              <option>Mobile App Development</option>
              <option>AI & Machine Learning</option>
              <option>Custom Software Solution</option>
              <option>Other</option>
            </select>
          </div>
          <div>
            <label class="block text-gray-700 font-medium mb-2">Project Details</label>
            <textarea rows="5" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none" placeholder="Tell us about your project, goals, and requirements..." required></textarea>
          </div>
          <div class="text-center">
            <button type="submit" class="btn-primary text-lg px-12 py-4">Send Message</button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="section-blue py-16">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid md:grid-cols-4 gap-8">
        <div class="md:col-span-2">
          <div class="flex items-center space-x-4 mb-6">
            <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10">
            <span class="font-space font-bold text-xl text-white">Vishnorex Technologies</span>
          </div>
          <p class="text-blue-100 leading-relaxed mb-6">
            Transforming innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">📧</a>
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">📱</a>
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">🌐</a>
          </div>
        </div>
        <div>
          <h4 class="font-bold text-white mb-4">Services</h4>
          <ul class="space-y-2 text-blue-100">
            <li><a href="#" class="hover:text-white transition-colors">Web Development</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Mobile Apps</a></li>
            <li><a href="#" class="hover:text-white transition-colors">AI Solutions</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Consulting</a></li>
          </ul>
        </div>
        <div>
          <h4 class="font-bold text-white mb-4">Company</h4>
          <ul class="space-y-2 text-blue-100">
            <li><a href="#about" class="hover:text-white transition-colors">About Us</a></li>
            <li><a href="#portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
            <li><a href="#contact" class="hover:text-white transition-colors">Contact</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-white/20 mt-12 pt-8 text-center">
        <p class="text-blue-100">&copy; 2024 Vishnorex Technologies. All rights reserved.</p>
      </div>
    </div>
  </footer>

</body>
</html>
