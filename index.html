<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies - Custom Software Solutions</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="./output.css" rel="stylesheet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body::-webkit-scrollbar {
      display: none;
    }
    body {
      -ms-overflow-style: none;
      scrollbar-width: none;
      font-family: 'Inter', sans-serif;
    }
    
    .font-space {
      font-family: 'Space Grotesk', sans-serif;
    }

    /* Modern Tech Company Design */
    .hero-section {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
      min-height: 100vh;
      position: relative;
      overflow: hidden;
    }
    
    .hero-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      opacity: 0.6;
    }

    /* Modern Navigation */
    .modern-nav {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }
    
    /* Tech Cards */
    .tech-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(59, 130, 246, 0.1);
    }
    
    .tech-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.2);
    }

    /* Modern Buttons */
    .btn-primary {
      background: linear-gradient(135deg, #3b82f6, #1e40af);
      color: white;
      padding: 14px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      background: linear-gradient(135deg, #1e40af, #1e3a8a);
    }
    
    .btn-secondary {
      background: white;
      color: #1e40af;
      padding: 14px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: 2px solid #3b82f6;
    }
    
    .btn-secondary:hover {
      background: #3b82f6;
      color: white;
      transform: translateY(-2px);
    }

    /* Text Styles */
    .text-gradient {
      background: linear-gradient(135deg, #1e40af, #3b82f6, #60a5fa);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    /* Section Styles */
    .section-light {
      background: #f8fafc;
    }
    
    .section-white {
      background: white;
    }
    
    .section-blue {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
      color: white;
    }
    
    /* Stats Counter */
    .stat-number {
      font-size: 3rem;
      font-weight: 800;
      color: #1e40af;
      line-height: 1;
    }
    
    /* Feature Icons */
    .feature-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #3b82f6, #60a5fa);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-bottom: 1rem;
    }

    /* Scroll Animation Effects */
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .fade-in.animate {
      opacity: 1;
      transform: translateY(0);
    }

    .slide-in-left {
      opacity: 0;
      transform: translateX(-50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-in-left.animate {
      opacity: 1;
      transform: translateX(0);
    }

    .slide-in-right {
      opacity: 0;
      transform: translateX(50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-in-right.animate {
      opacity: 1;
      transform: translateX(0);
    }

    .scale-in {
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .scale-in.animate {
      opacity: 1;
      transform: scale(1);
    }

    .rotate-in {
      opacity: 0;
      transform: rotate(-10deg) scale(0.9);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .rotate-in.animate {
      opacity: 1;
      transform: rotate(0deg) scale(1);
    }

    .bounce-in {
      opacity: 0;
      transform: translateY(-30px);
      transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    .bounce-in.animate {
      opacity: 1;
      transform: translateY(0);
    }

    .flip-in {
      opacity: 0;
      transform: rotateY(-90deg);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .flip-in.animate {
      opacity: 1;
      transform: rotateY(0deg);
    }

    .zoom-in {
      opacity: 0;
      transform: scale(0.5);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .zoom-in.animate {
      opacity: 1;
      transform: scale(1);
    }

    /* Staggered Animation Delays */
    .stagger-1 { transition-delay: 0.1s; }
    .stagger-2 { transition-delay: 0.2s; }
    .stagger-3 { transition-delay: 0.3s; }
    .stagger-4 { transition-delay: 0.4s; }
    .stagger-5 { transition-delay: 0.5s; }
    .stagger-6 { transition-delay: 0.6s; }

    /* Counter Animation */
    .counter {
      transition: all 0.8s ease-out;
    }

    /* Progress Bar Animation */
    .progress-bar {
      width: 0%;
      height: 4px;
      background: linear-gradient(90deg, #3b82f6, #60a5fa);
      border-radius: 2px;
      transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .progress-bar.animate {
      width: var(--progress-width);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero-section {
        padding: 2rem 1rem;
      }

      .stat-number {
        font-size: 2rem;
      }

      .tech-card {
        margin-bottom: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- Modern Tech Company Website -->
  
  <!-- Navigation -->
  <nav class="modern-nav fixed w-full z-50 top-0">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10">
          <span class="font-space font-bold text-xl text-gray-800">Vishnorex</span>
        </div>
        <div class="hidden md:flex space-x-8">
          <a href="#home" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Home</a>
          <a href="#about" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">About</a>
          <a href="#services" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Services</a>
          <a href="#portfolio" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Portfolio</a>
          <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Contact</a>
        </div>
        <a href="#contact" class="btn-primary hidden md:inline-block">Get Started</a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-pattern"></div>
    <div class="relative z-10 flex items-center justify-center min-h-screen px-6">
      <div class="text-center max-w-5xl mx-auto">
        <h1 class="text-5xl md:text-7xl font-space font-bold text-white mb-6 leading-tight">
          Custom Software Solutions to help Your Business 
          <span class="text-blue-200">Succeed</span>
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 mb-10 leading-relaxed max-w-3xl mx-auto">
          We transform innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="#contact" class="btn-primary text-lg px-8 py-4">Start Your Project</a>
          <a href="#about" class="btn-secondary text-lg px-8 py-4">Learn More</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="section-white py-20">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div class="bounce-in stagger-1">
          <div class="stat-number counter" data-target="50">0</div>
          <p class="text-gray-600 font-medium">Projects Completed</p>
        </div>
        <div class="bounce-in stagger-2">
          <div class="stat-number counter" data-target="25">0</div>
          <p class="text-gray-600 font-medium">Happy Clients</p>
        </div>
        <div class="bounce-in stagger-3">
          <div class="stat-number counter" data-target="3">0</div>
          <p class="text-gray-600 font-medium">Years Experience</p>
        </div>
        <div class="bounce-in stagger-4">
          <div class="stat-number">24/7</div>
          <p class="text-gray-600 font-medium">Support</p>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="section-light py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid md:grid-cols-2 gap-16 items-center">
        <div class="slide-in-left">
          <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">
            About Vishnorex Technologies
          </h2>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            We are a forward-thinking software development company specializing in custom solutions that drive business growth. Our team combines technical expertise with creative innovation to deliver exceptional digital experiences.
          </p>
          <div class="space-y-4">
            <div class="flex items-center space-x-3 fade-in stagger-1">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">Custom Software Development</span>
              <div class="progress-bar ml-auto" style="--progress-width: 95%;"></div>
            </div>
            <div class="flex items-center space-x-3 fade-in stagger-2">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">Mobile App Development</span>
              <div class="progress-bar ml-auto" style="--progress-width: 90%;"></div>
            </div>
            <div class="flex items-center space-x-3 fade-in stagger-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">AI & Machine Learning Solutions</span>
              <div class="progress-bar ml-auto" style="--progress-width: 85%;"></div>
            </div>
          </div>
        </div>
        <div class="relative slide-in-right">
          <div class="tech-card p-8 scale-in">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">Our Mission</h3>
            <p class="text-gray-600 leading-relaxed">
              To empower businesses with innovative technology solutions that transform ideas into reality and drive sustainable growth in the digital age.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="section-blue py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-white mb-6">Our Services</h2>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto">
          We offer comprehensive technology solutions to help your business thrive in the digital landscape.
        </p>
      </div>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20 flip-in stagger-1">
          <div class="feature-icon rotate-in">
            <span>🌐</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">Web Development</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Modern, responsive web applications built with React, Next.js, and cutting-edge frameworks for optimal performance.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-1">React</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-2">Next.js</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-3">TypeScript</span>
          </div>
        </div>
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20 flip-in stagger-2">
          <div class="feature-icon rotate-in">
            <span>📱</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">Mobile Apps</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Native iOS and Android applications with Flutter and React Native for seamless cross-platform experiences.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-1">Flutter</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-2">React Native</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-3">Swift</span>
          </div>
        </div>
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20 flip-in stagger-3">
          <div class="feature-icon rotate-in">
            <span>🤖</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">AI Solutions</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Intelligent solutions powered by machine learning, data analytics, and artificial intelligence technologies.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-1">TensorFlow</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-2">PyTorch</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-3">OpenAI</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="section-light py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="text-center mb-16 zoom-in">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">Featured Projects</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Discover some of our recent work and see how we've helped businesses transform their digital presence.
        </p>
      </div>
      <div class="grid md:grid-cols-2 gap-8">
        <div class="tech-card p-8 slide-in-left">
          <div class="text-5xl mb-6 bounce-in">🚀</div>
          <h3 class="text-2xl font-bold text-gray-800 mb-4 fade-in stagger-1">AI-Powered Travel Platform</h3>
          <p class="text-gray-600 mb-6 leading-relaxed fade-in stagger-2">
            Intelligent route optimization with machine learning algorithms, real-time booking, and personalized recommendations for enhanced user experience.
          </p>
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-1">React</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-2">Node.js</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-3">AI/ML</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-4">MongoDB</span>
          </div>
          <a href="#" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors fade-in stagger-3">View Project →</a>
        </div>
        <div class="tech-card p-8 slide-in-right">
          <div class="text-5xl mb-6 bounce-in">🏥</div>
          <h3 class="text-2xl font-bold text-gray-800 mb-4 fade-in stagger-1">Smart Healthcare Dashboard</h3>
          <p class="text-gray-600 mb-6 leading-relaxed fade-in stagger-2">
            Real-time patient monitoring with IoT integration, predictive analytics, and automated alert systems for improved healthcare delivery.
          </p>
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-1">Vue.js</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-2">Python</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-3">IoT</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-4">PostgreSQL</span>
          </div>
          <a href="#" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors fade-in stagger-3">View Project →</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="section-white py-24">
    <div class="max-w-4xl mx-auto px-6">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">Let's Build Something Amazing Together</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Ready to transform your ideas into reality? We'd love to hear about your project and discuss how we can help you succeed.
        </p>
      </div>
      <div class="tech-card p-12 scale-in">
        <form class="space-y-6">
          <div class="grid md:grid-cols-2 gap-6">
            <div class="slide-in-left stagger-1">
              <label class="block text-gray-700 font-medium mb-2">Your Name</label>
              <input type="text" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="John Doe" required>
            </div>
            <div class="slide-in-right stagger-1">
              <label class="block text-gray-700 font-medium mb-2">Email Address</label>
              <input type="email" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="<EMAIL>" required>
            </div>
          </div>
          <div class="fade-in stagger-2">
            <label class="block text-gray-700 font-medium mb-2">Project Type</label>
            <select class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
              <option>Web Development</option>
              <option>Mobile App Development</option>
              <option>AI & Machine Learning</option>
              <option>Custom Software Solution</option>
              <option>Other</option>
            </select>
          </div>
          <div class="fade-in stagger-3">
            <label class="block text-gray-700 font-medium mb-2">Project Details</label>
            <textarea rows="5" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none" placeholder="Tell us about your project, goals, and requirements..." required></textarea>
          </div>
          <div class="text-center bounce-in stagger-4">
            <button type="submit" class="btn-primary text-lg px-12 py-4">Send Message</button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="section-blue py-16">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid md:grid-cols-4 gap-8">
        <div class="md:col-span-2">
          <div class="flex items-center space-x-4 mb-6">
            <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10">
            <span class="font-space font-bold text-xl text-white">Vishnorex Technologies</span>
          </div>
          <p class="text-blue-100 leading-relaxed mb-6">
            Transforming innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">📧</a>
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">📱</a>
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">🌐</a>
          </div>
        </div>
        <div>
          <h4 class="font-bold text-white mb-4">Services</h4>
          <ul class="space-y-2 text-blue-100">
            <li><a href="#" class="hover:text-white transition-colors">Web Development</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Mobile Apps</a></li>
            <li><a href="#" class="hover:text-white transition-colors">AI Solutions</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Consulting</a></li>
          </ul>
        </div>
        <div>
          <h4 class="font-bold text-white mb-4">Company</h4>
          <ul class="space-y-2 text-blue-100">
            <li><a href="#about" class="hover:text-white transition-colors">About Us</a></li>
            <li><a href="#portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
            <li><a href="#contact" class="hover:text-white transition-colors">Contact</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-white/20 mt-12 pt-8 text-center">
        <p class="text-blue-100">&copy; 2024 Vishnorex Technologies. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scroll Animation JavaScript -->
  <script>
    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');

          // Handle counter animations
          if (entry.target.classList.contains('counter')) {
            animateCounter(entry.target);
          }

          // Handle progress bar animations
          if (entry.target.classList.contains('progress-bar')) {
            entry.target.classList.add('animate');
          }
        }
      });
    }, observerOptions);

    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in, .rotate-in, .bounce-in, .flip-in, .zoom-in, .counter, .progress-bar');

    animatedElements.forEach(el => {
      observer.observe(el);
    });

    // Counter animation function
    function animateCounter(element) {
      const target = parseInt(element.getAttribute('data-target'));
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;

      const updateCounter = () => {
        current += increment;
        if (current < target) {
          element.textContent = Math.floor(current) + '+';
          requestAnimationFrame(updateCounter);
        } else {
          element.textContent = target + '+';
        }
      };

      updateCounter();
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const heroPattern = document.querySelector('.hero-pattern');
      if (heroPattern) {
        heroPattern.style.transform = `translateY(${scrolled * 0.5}px)`;
      }
    });

    // Form submission handler
    document.querySelector('form').addEventListener('submit', function(e) {
      e.preventDefault();

      // Add loading state to button
      const button = this.querySelector('button[type="submit"]');
      const originalText = button.textContent;
      button.textContent = 'Sending...';
      button.disabled = true;

      // Simulate form submission
      setTimeout(() => {
        alert('Thank you for your message! We\'ll get back to you soon.');
        this.reset();
        button.textContent = originalText;
        button.disabled = false;
      }, 2000);
    });

    // Add loading animation to page
    window.addEventListener('load', () => {
      document.body.style.opacity = '0';
      document.body.style.transition = 'opacity 0.5s ease-in-out';

      setTimeout(() => {
        document.body.style.opacity = '1';
      }, 100);
    });
  </script>

</body>
</html>
