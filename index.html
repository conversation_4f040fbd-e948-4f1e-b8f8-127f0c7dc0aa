<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="./output.css" rel="stylesheet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Arvo:ital,wght@0,400;0,700;1,400;1,700&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap');
    body::-webkit-scrollbar {
      display: none;
    }
    body {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    .font-arvo {
      font-family: 'Arvo', serif;
    }

    /* Animated Line Background */
    .animated-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 50%, #9ca3af 100%);
      overflow: hidden;
    }

    .line {
      position: absolute;
      background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.7), transparent);
      animation: moveLine 4s linear infinite;
    }

    .line:nth-child(1) {
      width: 200px;
      height: 2px;
      top: 20%;
      left: -200px;
      animation-delay: 0s;
      animation-duration: 6s;
    }

    .line:nth-child(2) {
      width: 150px;
      height: 1px;
      top: 40%;
      left: -150px;
      animation-delay: 2s;
      animation-duration: 5s;
      background: linear-gradient(90deg, transparent, rgba(15, 23, 42, 0.8), transparent);
    }

    .line:nth-child(3) {
      width: 300px;
      height: 3px;
      top: 60%;
      left: -300px;
      animation-delay: 4s;
      animation-duration: 7s;
      background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.6), transparent);
    }

    .line:nth-child(4) {
      width: 180px;
      height: 1px;
      top: 80%;
      left: -180px;
      animation-delay: 6s;
      animation-duration: 5.5s;
      background: linear-gradient(90deg, transparent, rgba(29, 78, 216, 0.7), transparent);
    }

    .line:nth-child(5) {
      width: 250px;
      height: 2px;
      top: 30%;
      left: -250px;
      animation-delay: 1s;
      animation-duration: 6.5s;
      background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.65), transparent);
    }

    .line:nth-child(6) {
      width: 120px;
      height: 1px;
      top: 70%;
      left: -120px;
      animation-delay: 3s;
      animation-duration: 4.5s;
      background: linear-gradient(90deg, transparent, rgba(15, 23, 42, 0.75), transparent);
    }

    /* Vertical lines */
    .v-line {
      position: absolute;
      background: linear-gradient(180deg, transparent, rgba(30, 58, 138, 0.6), transparent);
      animation: moveVLine 5s linear infinite;
    }

    .v-line:nth-child(7) {
      width: 1px;
      height: 200px;
      left: 20%;
      top: -200px;
      animation-delay: 1s;
      animation-duration: 7.5s;
    }

    .v-line:nth-child(8) {
      width: 2px;
      height: 150px;
      left: 60%;
      top: -150px;
      animation-delay: 3s;
      animation-duration: 6s;
      background: linear-gradient(180deg, transparent, rgba(15, 23, 42, 0.65), transparent);
    }

    .v-line:nth-child(9) {
      width: 1px;
      height: 180px;
      left: 80%;
      top: -180px;
      animation-delay: 5s;
      animation-duration: 5.5s;
      background: linear-gradient(180deg, transparent, rgba(29, 78, 216, 0.7), transparent);
    }

    @keyframes moveLine {
      0% {
        transform: translateX(0);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateX(calc(100vw + 300px));
        opacity: 0;
      }
    }

    @keyframes moveVLine {
      0% {
        transform: translateY(0);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(calc(100vh + 200px));
        opacity: 0;
      }
    }



  </style>
</head>
<body class=" text-gray-800 font-sans">

<div class="relative h-screen overflow-hidden">
    <!-- Animated Line Background -->
    <div class="animated-bg">
        <!-- Horizontal lines -->
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>

        <!-- Vertical lines -->
        <div class="v-line"></div>
        <div class="v-line"></div>
        <div class="v-line"></div>

        <!-- Floating particles -->
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="relative z-10 h-full flex flex-col">
        <!-- Navbar -->
        <header class="bg-white bg-opacity-20 fixed w-full z-50 top-0">
            <div class="max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
                <img src="./assests/nobg.png" alt="Vishnorex Logo" class="h-12">
                <nav class="space-x-6 hidden md:flex">
                    <a href="#about" class="text-midnightblue-800 font-arvo hover:text-blue-500">About</a>
                    <a href="#services" class="text-blue-800 font-arvo hover:text-blue-500">Services</a>
                    <a href="#projects" class="text-blue-800 font-arvo hover:text-blue-500">Projects</a>
                    <a href="#contact" class="text-blue-800 font-arvo hover:text-blue-500">Contact</a>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="flex-grow flex items-center justify-center text-center">
            <div class="max-w-4xl mx-auto px-6">
                <h2 class="text-4xl md:text-5xl font-extrabold text-gradient mb-6">We Build Digital Dreams</h2>
                <p class="text-lg text-blue-950 mb-8">Mobile-first solutions with precision, performance, and purpose.</p>
                <a href="#contact" class="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition">Start Your Project</a>
            </div>
        </section>
    </div>
</div>

  <!-- About Section -->
  <section id="about" class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-4">About Vishnorex</h3>
      <p class="text-gray-600 text-lg">We specialize in mobile app development, web design, and branding. Our mission is to turn bold ideas into powerful digital experiences.</p>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="py-20 bg-gray-100">
    <div class="max-w-6xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-12">Our Services</h3>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Web Development</h4>
          <p class="text-gray-600">Responsive websites using HTML, Tailwind CSS, and JavaScript.</p>
        </div>
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Mobile App Design</h4>
          <p class="text-gray-600">Intuitive UI/UX for Android and iOS platforms.</p>
        </div>
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Brand Identity</h4>
          <p class="text-gray-600">Professional seals, logos, and visual branding.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Projects Section -->
  <section id="projects" class="py-20 bg-white">
    <div class="max-w-6xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-12">Featured Projects</h3>
      <div class="grid md:grid-cols-2 gap-8">
        <div class="bg-gray-100 p-6 rounded-lg shadow">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Travel Management System</h4>
          <p class="text-gray-600">Smart route planning and booking dashboard.</p>
        </div>
        <div class="bg-gray-100 p-6 rounded-lg shadow">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Hospital Monitoring Dashboard</h4>
          <p class="text-gray-600">Real-time health tracking with backend integration.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="py-20 bg-indigo-100">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-700 mb-6">Let’s Connect</h3>
      <form id="contactForm" class="space-y-4">
        <input type="text" placeholder="Your Name" class="w-full p-3 rounded border border-gray-300" required />
        <input type="email" placeholder="Your Email" class="w-full p-3 rounded border border-gray-300" required />
        <textarea placeholder="Your Message" class="w-full p-3 rounded border border-gray-300" rows="4" required></textarea>
        <button type="submit" class="bg-indigo-600 text-white px-6 py-3 rounded hover:bg-indigo-700 transition">Send Message</button>
      </form>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-white py-6 text-center border-t">
    <p class="text-sm text-gray-500">© 2025 Vishnorex Technologies Private Limited. All rights reserved.</p>
  </footer>

  <!-- JavaScript -->
  <script>
    document.getElementById("contactForm").addEventListener("submit", function(e) {
      e.preventDefault();
      alert("Thanks for contacting Vishnorex! We'll get back to you soon.");
    });
  </script>

</body>
</html>
