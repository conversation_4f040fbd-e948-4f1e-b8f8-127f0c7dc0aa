<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="./output.css" rel="stylesheet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Arvo:ital,wght@0,400;0,700;1,400;1,700&family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap');
    body::-webkit-scrollbar {
      display: none;
    }
    body {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
    .font-arvo {
      font-family: 'Arvo', serif;
    }

    /* Modern Futuristic Background */
    .futuristic-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
      overflow: hidden;
      z-index: 0;
    }

    /* Glassmorphism Navbar */
    .glass-nav {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    /* Floating Geometric Shapes */
    .floating-shape {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      animation: floatAnimation 20s ease-in-out infinite;
    }

    .shape-1 {
      width: 100px;
      height: 100px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: 15%;
      animation-delay: 5s;
      border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }

    .shape-3 {
      width: 80px;
      height: 80px;
      bottom: 20%;
      left: 20%;
      animation-delay: 10s;
    }

    .shape-4 {
      width: 120px;
      height: 120px;
      top: 30%;
      right: 30%;
      animation-delay: 15s;
      border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }

    /* Neural Network Lines */
    .neural-line {
      position: absolute;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(120, 219, 255, 0.6), transparent);
      animation: neuralFlow 8s linear infinite;
      box-shadow: 0 0 10px rgba(120, 219, 255, 0.3);
    }

    .neural-line:nth-child(1) {
      width: 300px;
      top: 25%;
      left: -300px;
      animation-delay: 0s;
    }

    .neural-line:nth-child(2) {
      width: 250px;
      top: 45%;
      left: -250px;
      animation-delay: 2s;
      background: linear-gradient(90deg, transparent, rgba(255, 119, 198, 0.6), transparent);
      box-shadow: 0 0 10px rgba(255, 119, 198, 0.3);
    }

    .neural-line:nth-child(3) {
      width: 350px;
      top: 65%;
      left: -350px;
      animation-delay: 4s;
      background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.6), transparent);
      box-shadow: 0 0 10px rgba(120, 119, 198, 0.3);
    }

    /* Data Particles */
    .data-particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(120, 219, 255, 0.8);
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(120, 219, 255, 0.5);
      animation: particleFloat 15s ease-in-out infinite;
    }

    .particle-1 {
      top: 20%;
      left: 15%;
      animation-delay: 0s;
    }

    .particle-2 {
      top: 40%;
      right: 20%;
      animation-delay: 3s;
      background: rgba(255, 119, 198, 0.8);
      box-shadow: 0 0 10px rgba(255, 119, 198, 0.5);
    }

    .particle-3 {
      bottom: 30%;
      left: 30%;
      animation-delay: 6s;
      background: rgba(120, 119, 198, 0.8);
      box-shadow: 0 0 10px rgba(120, 119, 198, 0.5);
    }

    .particle-4 {
      top: 70%;
      right: 40%;
      animation-delay: 9s;
    }

    .particle-5 {
      top: 50%;
      left: 60%;
      animation-delay: 12s;
      background: rgba(255, 119, 198, 0.8);
      box-shadow: 0 0 10px rgba(255, 119, 198, 0.5);
    }

    /* Holographic Grid */
    .holo-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(rgba(120, 219, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(120, 219, 255, 0.1) 1px, transparent 1px);
      background-size: 100px 100px;
      animation: gridShift 30s linear infinite;
      opacity: 0.3;
    }

    /* Animation Keyframes */
    @keyframes floatAnimation {
      0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.7;
      }
      25% {
        transform: translateY(-20px) rotate(90deg) scale(1.1);
        opacity: 1;
      }
      50% {
        transform: translateY(-40px) rotate(180deg) scale(0.9);
        opacity: 0.8;
      }
      75% {
        transform: translateY(-20px) rotate(270deg) scale(1.05);
        opacity: 0.9;
      }
    }

    @keyframes neuralFlow {
      0% {
        transform: translateX(0) scaleX(0);
        opacity: 0;
      }
      20% {
        transform: translateX(0) scaleX(1);
        opacity: 1;
      }
      80% {
        transform: translateX(calc(100vw + 100px)) scaleX(1);
        opacity: 1;
      }
      100% {
        transform: translateX(calc(100vw + 350px)) scaleX(0);
        opacity: 0;
      }
    }

    @keyframes particleFloat {
      0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.8;
      }
      25% {
        transform: translate(30px, -40px) scale(1.2);
        opacity: 1;
      }
      50% {
        transform: translate(-20px, -80px) scale(0.8);
        opacity: 0.6;
      }
      75% {
        transform: translate(40px, -60px) scale(1.1);
        opacity: 0.9;
      }
    }

    @keyframes gridShift {
      0% {
        transform: translate(0, 0);
      }
      100% {
        transform: translate(100px, 100px);
      }
    }

    /* Glassmorphism Hero Card */
    .glass-hero {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      padding: 3rem;
      margin: 2rem;
    }

    /* Glassmorphism Service Cards */
    .glass-card {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 16px;
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }

    .glass-card:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.12);
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    /* Modern Button */
    .modern-btn {
      background: linear-gradient(135deg, rgba(120, 219, 255, 0.2), rgba(255, 119, 198, 0.2));
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 50px;
      color: white;
      padding: 12px 30px;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .modern-btn:hover {
      transform: translateY(-2px);
      background: linear-gradient(135deg, rgba(120, 219, 255, 0.3), rgba(255, 119, 198, 0.3));
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    /* Text Gradient */
    .gradient-text {
      background: linear-gradient(135deg, #78dbff, #ff77c6, #7877c6);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .floating-shape {
        transform: scale(0.7);
      }
      .glass-hero {
        padding: 2rem;
        margin: 1rem;
      }
    }

  </style>
</head>
<body class=" text-gray-800 font-sans">

<div class="relative h-screen overflow-hidden">
   
    <div class="relative z-10 h-full flex flex-col">
        <!-- Navbar -->
        <header class="bg-white bg-opacity-20 fixed w-full z-50 top-0">
            <div class="max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
                <img src="./assests/nobg.png" alt="Vishnorex Logo" class="h-12">
                <nav class="space-x-6 hidden md:flex">
                    <a href="#about" class="text-blue-800 font-arvo hover:text-blue-500">About</a>
                    <a href="#services" class="text-blue-800 font-arvo hover:text-blue-500">Services</a>
                    <a href="#projects" class="text-blue-800 font-arvo hover:text-blue-500">Projects</a>
                    <a href="#contact" class="text-blue-800 font-arvo hover:text-blue-500">Contact</a>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="flex-grow flex items-center justify-center text-center">
            <div class="max-w-4xl mx-auto px-6">
                <h2 class="text-4xl md:text-5xl font-extrabold text-gradient mb-6">We Build Digital Dreams</h2>
                <p class="text-lg text-blue-950 mb-8">Mobile-first solutions with precision, performance, and purpose.</p>
                <a href="#contact" class="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition">Start Your Project</a>
            </div>
        </section>
    </div>
</div>

  <!-- About Section -->
  <section id="about" class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-4">About Vishnorex</h3>
      <p class="text-gray-600 text-lg">We specialize in mobile app development, web design, and branding. Our mission is to turn bold ideas into powerful digital experiences.</p>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="py-20 bg-gray-100">
    <div class="max-w-6xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-600 mb-12">Our Services</h3>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Web Development</h4>
          <p class="text-gray-600">Responsive websites using HTML, Tailwind CSS, and JavaScript.</p>
        </div>
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Mobile App Design</h4>
          <p class="text-gray-600">Intuitive UI/UX for Android and iOS platforms.</p>
        </div>
        <div class="bg-white p-8 rounded-lg shadow hover:shadow-xl transition">
          <h4 class="text-xl font-semibold text-indigo-700 mb-2">Brand Identity</h4>
          <p class="text-gray-600">Professional seals, logos, and visual branding.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Projects Section with Glassmorphism -->
  <section id="projects" class="py-20 relative" style="background: linear-gradient(135deg, #16213e 0%, #0f3460 50%, #533483 100%);">
    <div class="max-w-6xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold gradient-text mb-12">Featured Projects</h3>
      <div class="grid md:grid-cols-2 gap-8">
        <div class="glass-card p-8">
          <div class="text-5xl mb-4">🚀</div>
          <h4 class="text-xl font-semibold text-white mb-4">AI-Powered Travel Platform</h4>
          <p class="text-white/70 mb-4">Intelligent route optimization with machine learning algorithms, real-time booking, and personalized recommendations.</p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80">React</span>
            <span class="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80">Node.js</span>
            <span class="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80">AI/ML</span>
          </div>
        </div>
        <div class="glass-card p-8">
          <div class="text-5xl mb-4">🏥</div>
          <h4 class="text-xl font-semibold text-white mb-4">Smart Healthcare Dashboard</h4>
          <p class="text-white/70 mb-4">Real-time patient monitoring with IoT integration, predictive analytics, and automated alert systems.</p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80">Vue.js</span>
            <span class="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80">Python</span>
            <span class="px-3 py-1 bg-white/10 rounded-full text-xs text-white/80">IoT</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="py-20 bg-indigo-100">
    <div class="max-w-4xl mx-auto px-6 text-center">
      <h3 class="text-3xl font-bold text-indigo-700 mb-6">Let’s Connect</h3>
      <form id="contactForm" class="space-y-4">
        <input type="text" placeholder="Your Name" class="w-full p-3 rounded border border-gray-300" required />
        <input type="email" placeholder="Your Email" class="w-full p-3 rounded border border-gray-300" required />
        <textarea placeholder="Your Message" class="w-full p-3 rounded border border-gray-300" rows="4" required></textarea>
        <button type="submit" class="bg-indigo-600 text-white px-6 py-3 rounded hover:bg-indigo-700 transition">Send Message</button>
      </form>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-white py-6 text-center border-t">
    <p class="text-sm text-gray-500">© 2025 Vishnorex Technologies Private Limited. All rights reserved.</p>
  </footer>

  <!-- JavaScript -->
  <script>
    document.getElementById("contactForm").addEventListener("submit", function(e) {
      e.preventDefault();
      alert("Thanks for contacting Vishnorex! We'll get back to you soon.");
    });
  </script>

</body>
</html>
