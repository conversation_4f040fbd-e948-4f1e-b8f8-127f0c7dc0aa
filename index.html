<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies - Custom Software Solutions</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'midnight-blue': '#191970',
            'vishnorex-blue': '#191970',
          },
          fontFamily: {
            'inter': ['Inter', 'sans-serif'],
            'space': ['Space Grotesk', 'sans-serif'],
          }
        }
      }
    }
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', sans-serif;
      line-height: 1.6;
      color: #333;
    }
    
    .font-space {
      font-family: 'Space Grotesk', sans-serif;
    }
    
    /* Smooth scrolling */
    html {
      scroll-behavior: smooth;
    }
    
    /* Custom animations */
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.6s ease-out;
    }
    
    .fade-in.animate {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Hero section styling */
    .hero-gradient {
      background: linear-gradient(135deg, #191970 0%, #2c3e95 100%);
    }
    
    /* Service card hover effects */
    .service-card {
      transition: all 0.3s ease;
    }
    
    .service-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(25, 25, 112, 0.15);
    }
    
    /* Button styles */
    .btn-primary {
      background: #191970;
      color: white;
      padding: 14px 32px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }
    
    .btn-primary:hover {
      background: #2c3e95;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(25, 25, 112, 0.3);
    }
    
    .btn-outline {
      background: transparent;
      color: #191970;
      padding: 14px 32px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: 2px solid #191970;
    }
    
    .btn-outline:hover {
      background: #191970;
      color: white;
      transform: translateY(-2px);
    }
    
    /* Navigation styles */
    .navbar {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }
    
    .navbar.scrolled {
      box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }
    
    /* Section spacing */
    .section-padding {
      padding: 80px 0;
    }
    
    @media (max-width: 768px) {
      .section-padding {
        padding: 60px 0;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar fixed w-full top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10 w-auto">
          <span class="ml-3 text-xl font-bold text-midnight-blue font-space">Vishnorex</span>
        </div>
        
        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <a href="#home" class="text-gray-700 hover:text-midnight-blue font-medium transition-colors">Home</a>
          <a href="#services" class="text-gray-700 hover:text-midnight-blue font-medium transition-colors">Services</a>
          <a href="#about" class="text-gray-700 hover:text-midnight-blue font-medium transition-colors">About</a>
          <a href="#contact" class="text-gray-700 hover:text-midnight-blue font-medium transition-colors">Contact</a>
        </div>
        
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button id="mobile-menu-btn" class="text-gray-700 hover:text-midnight-blue">
            <i class="fas fa-bars text-xl"></i>
          </button>
        </div>
      </div>
      
      <!-- Mobile Navigation -->
      <div id="mobile-menu" class="md:hidden hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t">
          <a href="#home" class="block px-3 py-2 text-gray-700 hover:text-midnight-blue">Home</a>
          <a href="#services" class="block px-3 py-2 text-gray-700 hover:text-midnight-blue">Services</a>
          <a href="#about" class="block px-3 py-2 text-gray-700 hover:text-midnight-blue">About</a>
          <a href="#contact" class="block px-3 py-2 text-gray-700 hover:text-midnight-blue">Contact</a>
        </div>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero-gradient min-h-screen flex items-center justify-center text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <div class="fade-in">
        <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold font-space mb-6 leading-tight">
          Custom Software Solutions
          <span class="block text-blue-200">for Your Business</span>
        </h1>
        <p class="text-xl md:text-2xl mb-10 max-w-3xl mx-auto leading-relaxed opacity-90">
          We transform innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="#contact" class="btn-primary text-lg">Get Started</a>
          <a href="#services" class="btn-outline text-lg bg-white text-midnight-blue hover:bg-midnight-blue hover:text-white">Our Services</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="section-padding bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-midnight-blue font-space mb-6">
          Our Services
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          We offer comprehensive technology solutions to help your business thrive in the digital landscape.
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Web Development -->
        <div class="service-card bg-white p-8 rounded-lg shadow-lg border border-gray-100 fade-in">
          <div class="w-16 h-16 bg-midnight-blue rounded-lg flex items-center justify-center mb-6">
            <i class="fas fa-code text-white text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-midnight-blue mb-4">Web Development</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Modern, responsive web applications built with React, Next.js, and cutting-edge frameworks for optimal performance.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">React</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Next.js</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">TypeScript</span>
          </div>
        </div>

        <!-- Mobile Development -->
        <div class="service-card bg-white p-8 rounded-lg shadow-lg border border-gray-100 fade-in">
          <div class="w-16 h-16 bg-midnight-blue rounded-lg flex items-center justify-center mb-6">
            <i class="fas fa-mobile-alt text-white text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-midnight-blue mb-4">Mobile Apps</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Native iOS and Android applications with Flutter and React Native for seamless cross-platform experiences.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Flutter</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">React Native</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Swift</span>
          </div>
        </div>

        <!-- AI Solutions -->
        <div class="service-card bg-white p-8 rounded-lg shadow-lg border border-gray-100 fade-in">
          <div class="w-16 h-16 bg-midnight-blue rounded-lg flex items-center justify-center mb-6">
            <i class="fas fa-brain text-white text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-midnight-blue mb-4">AI Solutions</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Intelligent solutions powered by machine learning, data analytics, and artificial intelligence technologies.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">TensorFlow</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">PyTorch</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">OpenAI</span>
          </div>
        </div>

        <!-- Cloud Solutions -->
        <div class="service-card bg-white p-8 rounded-lg shadow-lg border border-gray-100 fade-in">
          <div class="w-16 h-16 bg-midnight-blue rounded-lg flex items-center justify-center mb-6">
            <i class="fas fa-cloud text-white text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-midnight-blue mb-4">Cloud Solutions</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Scalable cloud infrastructure and deployment solutions using AWS, Azure, and Google Cloud Platform.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">AWS</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Azure</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">GCP</span>
          </div>
        </div>

        <!-- DevOps -->
        <div class="service-card bg-white p-8 rounded-lg shadow-lg border border-gray-100 fade-in">
          <div class="w-16 h-16 bg-midnight-blue rounded-lg flex items-center justify-center mb-6">
            <i class="fas fa-cogs text-white text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-midnight-blue mb-4">DevOps</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Streamlined development and deployment processes with CI/CD pipelines and automated testing.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Docker</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Kubernetes</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Jenkins</span>
          </div>
        </div>

        <!-- Consulting -->
        <div class="service-card bg-white p-8 rounded-lg shadow-lg border border-gray-100 fade-in">
          <div class="w-16 h-16 bg-midnight-blue rounded-lg flex items-center justify-center mb-6">
            <i class="fas fa-lightbulb text-white text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-midnight-blue mb-4">IT Consulting</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Strategic technology consulting to help you make informed decisions and optimize your digital transformation.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Strategy</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Architecture</span>
            <span class="px-3 py-1 bg-blue-100 text-midnight-blue text-sm rounded-full">Optimization</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="section-padding bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <!-- Image Side -->
        <div class="fade-in">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                 alt="Team collaboration"
                 class="rounded-lg shadow-xl w-full h-96 object-cover">
            <div class="absolute inset-0 bg-midnight-blue bg-opacity-10 rounded-lg"></div>
          </div>
        </div>

        <!-- Content Side -->
        <div class="fade-in">
          <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-midnight-blue font-space mb-6">
            About Vishnorex Technologies
          </h2>
          <p class="text-lg text-gray-600 mb-6 leading-relaxed">
            We are a forward-thinking software development company specializing in custom solutions that drive business growth. Our team combines technical expertise with creative innovation to deliver exceptional digital experiences.
          </p>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            Founded with a vision to transform how businesses leverage technology, we've helped over 50 companies achieve their digital transformation goals through innovative software solutions.
          </p>

          <!-- Stats -->
          <div class="grid grid-cols-3 gap-6 mb-8">
            <div class="text-center">
              <div class="text-3xl font-bold text-midnight-blue mb-2">50+</div>
              <div class="text-sm text-gray-600">Projects Completed</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-midnight-blue mb-2">25+</div>
              <div class="text-sm text-gray-600">Happy Clients</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-midnight-blue mb-2">3+</div>
              <div class="text-sm text-gray-600">Years Experience</div>
            </div>
          </div>

          <!-- Features -->
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="w-6 h-6 bg-midnight-blue rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-check text-white text-sm"></i>
              </div>
              <span class="text-gray-700">Custom Software Development</span>
            </div>
            <div class="flex items-center">
              <div class="w-6 h-6 bg-midnight-blue rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-check text-white text-sm"></i>
              </div>
              <span class="text-gray-700">24/7 Support & Maintenance</span>
            </div>
            <div class="flex items-center">
              <div class="w-6 h-6 bg-midnight-blue rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-check text-white text-sm"></i>
              </div>
              <span class="text-gray-700">Agile Development Process</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="section-padding bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-midnight-blue font-space mb-6">
          Get In Touch
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Ready to transform your ideas into reality? Let's discuss how we can help you achieve your goals.
        </p>
      </div>

      <div class="grid lg:grid-cols-2 gap-12">
        <!-- Contact Form -->
        <div class="fade-in">
          <form class="space-y-6" id="contactForm">
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                <input type="text" id="firstName" name="firstName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-midnight-blue focus:border-transparent transition-all">
              </div>
              <div>
                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                <input type="text" id="lastName" name="lastName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-midnight-blue focus:border-transparent transition-all">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-midnight-blue focus:border-transparent transition-all">
            </div>

            <div>
              <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company</label>
              <input type="text" id="company" name="company"
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-midnight-blue focus:border-transparent transition-all">
            </div>

            <div>
              <label for="service" class="block text-sm font-medium text-gray-700 mb-2">Service Interested In</label>
              <select id="service" name="service" required
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-midnight-blue focus:border-transparent transition-all">
                <option value="">Select a service</option>
                <option value="web-development">Web Development</option>
                <option value="mobile-apps">Mobile Apps</option>
                <option value="ai-solutions">AI Solutions</option>
                <option value="cloud-solutions">Cloud Solutions</option>
                <option value="devops">DevOps</option>
                <option value="consulting">IT Consulting</option>
              </select>
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
              <textarea id="message" name="message" rows="5" required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-midnight-blue focus:border-transparent transition-all resize-none"
                        placeholder="Tell us about your project..."></textarea>
            </div>

            <button type="submit" class="btn-primary w-full text-lg">
              Send Message
            </button>
          </form>
        </div>

        <!-- Contact Information -->
        <div class="fade-in">
          <div class="bg-gray-50 p-8 rounded-lg">
            <h3 class="text-2xl font-bold text-midnight-blue mb-6">Contact Information</h3>

            <div class="space-y-6">
              <div class="flex items-start">
                <div class="w-12 h-12 bg-midnight-blue rounded-lg flex items-center justify-center mr-4">
                  <i class="fas fa-map-marker-alt text-white"></i>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">Address</h4>
                  <p class="text-gray-600">123 Tech Street, Innovation District<br>Bangalore, Karnataka 560001</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="w-12 h-12 bg-midnight-blue rounded-lg flex items-center justify-center mr-4">
                  <i class="fas fa-phone text-white"></i>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">Phone</h4>
                  <p class="text-gray-600">+91 98765 43210</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="w-12 h-12 bg-midnight-blue rounded-lg flex items-center justify-center mr-4">
                  <i class="fas fa-envelope text-white"></i>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">Email</h4>
                  <p class="text-gray-600"><EMAIL></p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="w-12 h-12 bg-midnight-blue rounded-lg flex items-center justify-center mr-4">
                  <i class="fas fa-clock text-white"></i>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900 mb-1">Business Hours</h4>
                  <p class="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-midnight-blue text-white section-padding">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="md:col-span-2">
          <div class="flex items-center mb-6">
            <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10 w-auto">
            <span class="ml-3 text-xl font-bold font-space">Vishnorex Technologies</span>
          </div>
          <p class="text-blue-200 leading-relaxed mb-6 max-w-md">
            Transforming innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center hover:bg-opacity-20 transition-colors">
              <i class="fab fa-linkedin text-white"></i>
            </a>
            <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center hover:bg-opacity-20 transition-colors">
              <i class="fab fa-twitter text-white"></i>
            </a>
            <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center hover:bg-opacity-20 transition-colors">
              <i class="fab fa-github text-white"></i>
            </a>
            <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-lg flex items-center justify-center hover:bg-opacity-20 transition-colors">
              <i class="fab fa-instagram text-white"></i>
            </a>
          </div>
        </div>

        <!-- Services -->
        <div>
          <h4 class="font-bold text-white mb-4">Services</h4>
          <ul class="space-y-2 text-blue-200">
            <li><a href="#services" class="hover:text-white transition-colors">Web Development</a></li>
            <li><a href="#services" class="hover:text-white transition-colors">Mobile Apps</a></li>
            <li><a href="#services" class="hover:text-white transition-colors">AI Solutions</a></li>
            <li><a href="#services" class="hover:text-white transition-colors">Cloud Solutions</a></li>
            <li><a href="#services" class="hover:text-white transition-colors">DevOps</a></li>
            <li><a href="#services" class="hover:text-white transition-colors">IT Consulting</a></li>
          </ul>
        </div>

        <!-- Company -->
        <div>
          <h4 class="font-bold text-white mb-4">Company</h4>
          <ul class="space-y-2 text-blue-200">
            <li><a href="#about" class="hover:text-white transition-colors">About Us</a></li>
            <li><a href="#contact" class="hover:text-white transition-colors">Contact</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Terms of Service</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-white border-opacity-20 mt-12 pt-8 text-center">
        <p class="text-blue-200">&copy; 2024 Vishnorex Technologies. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');

    mobileMenuBtn.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
      const navbar = document.querySelector('.navbar');
      if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          // Close mobile menu if open
          mobileMenu.classList.add('hidden');
        }
      });
    });

    // Intersection Observer for fade-in animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');
        }
      });
    }, observerOptions);

    // Observe all fade-in elements
    document.querySelectorAll('.fade-in').forEach(el => {
      observer.observe(el);
    });

    // Contact form submission
    document.getElementById('contactForm').addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form data
      const formData = new FormData(this);
      const data = Object.fromEntries(formData);

      // Simulate form submission
      alert('Thank you for your message! We will get back to you soon.');
      this.reset();
    });

    // Initialize animations on page load
    window.addEventListener('load', () => {
      // Trigger hero section animation
      document.querySelector('#home .fade-in').classList.add('animate');
    });
  </script>

</body>
</html>
