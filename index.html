<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vishnorex Technologies - Custom Software Solutions</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="./output.css" rel="stylesheet">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body::-webkit-scrollbar {
      display: none;
    }
    body {
      -ms-overflow-style: none;
      scrollbar-width: none;
      font-family: 'Inter', sans-serif;
    }
    
    .font-space {
      font-family: 'Space Grotesk', sans-serif;
    }

    /* Modern Tech Company Design with Background Image */
    .hero-section {
      background:
        linear-gradient(135deg, rgba(30, 64, 175, 0.85) 0%, rgba(59, 130, 246, 0.8) 50%, rgba(96, 165, 250, 0.75) 100%),
        url('https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2072&q=80') center/cover no-repeat;
      min-height: 100vh;
      position: relative;
      overflow: hidden;
      background-attachment: fixed;
    }

    .hero-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
      opacity: 0.8;
      animation: patternMove 20s ease-in-out infinite;
    }

    /* Animated geometric overlay */
    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(45deg, transparent 40%, rgba(59, 130, 246, 0.1) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(96, 165, 250, 0.08) 50%, transparent 60%);
      background-size: 100px 100px, 150px 150px;
      animation: overlayShift 25s linear infinite;
    }

    /* Floating particles effect */
    .hero-particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(147, 197, 253, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(59, 130, 246, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(96, 165, 250, 0.3), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: particlesFloat 30s linear infinite;
    }

    @keyframes patternMove {
      0%, 100% { transform: translateX(0) translateY(0); }
      25% { transform: translateX(20px) translateY(-10px); }
      50% { transform: translateX(-10px) translateY(20px); }
      75% { transform: translateX(15px) translateY(10px); }
    }

    @keyframes overlayShift {
      0% { transform: translateX(0) translateY(0); }
      100% { transform: translateX(100px) translateY(100px); }
    }

    @keyframes particlesFloat {
      0% { transform: translateY(0); }
      100% { transform: translateY(-100px); }
    }

    /* Services Section Background Effects */
    .services-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.04) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
      opacity: 0.7;
      animation: servicesPatternMove 22s ease-in-out infinite;
    }

    .services-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(45deg, transparent 40%, rgba(59, 130, 246, 0.08) 50%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(96, 165, 250, 0.06) 50%, transparent 60%);
      background-size: 120px 120px, 180px 180px;
      animation: servicesOverlayShift 28s linear infinite;
    }

    .services-particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(1px 1px at 30px 40px, rgba(255, 255, 255, 0.25), transparent),
        radial-gradient(1px 1px at 80px 90px, rgba(147, 197, 253, 0.3), transparent),
        radial-gradient(2px 2px at 140px 60px, rgba(59, 130, 246, 0.2), transparent),
        radial-gradient(1px 1px at 190px 120px, rgba(255, 255, 255, 0.15), transparent);
      background-repeat: repeat;
      background-size: 220px 160px;
      animation: servicesParticlesFloat 35s linear infinite;
    }

    /* Footer Section Background Effects */
    .footer-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.04) 0%, transparent 50%),
        linear-gradient(135deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
      opacity: 0.6;
      animation: footerPatternMove 24s ease-in-out infinite;
    }

    .footer-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(60deg, transparent 40%, rgba(59, 130, 246, 0.06) 50%, transparent 60%),
        linear-gradient(-60deg, transparent 40%, rgba(96, 165, 250, 0.04) 50%, transparent 60%);
      background-size: 140px 140px, 200px 200px;
      animation: footerOverlayShift 32s linear infinite;
    }

    .footer-particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(1px 1px at 40px 30px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(2px 2px at 100px 80px, rgba(147, 197, 253, 0.25), transparent),
        radial-gradient(1px 1px at 160px 50px, rgba(59, 130, 246, 0.15), transparent),
        radial-gradient(1px 1px at 220px 100px, rgba(255, 255, 255, 0.1), transparent);
      background-repeat: repeat;
      background-size: 260px 130px;
      animation: footerParticlesFloat 40s linear infinite;
    }

    /* Animation keyframes for Services */
    @keyframes servicesPatternMove {
      0%, 100% { transform: translateX(0) translateY(0); }
      25% { transform: translateX(15px) translateY(-8px); }
      50% { transform: translateX(-8px) translateY(15px); }
      75% { transform: translateX(12px) translateY(8px); }
    }

    @keyframes servicesOverlayShift {
      0% { transform: translateX(0) translateY(0); }
      100% { transform: translateX(120px) translateY(120px); }
    }

    @keyframes servicesParticlesFloat {
      0% { transform: translateY(0); }
      100% { transform: translateY(-160px); }
    }

    /* Animation keyframes for Footer */
    @keyframes footerPatternMove {
      0%, 100% { transform: translateX(0) translateY(0); }
      25% { transform: translateX(-12px) translateY(10px); }
      50% { transform: translateX(10px) translateY(-12px); }
      75% { transform: translateX(-8px) translateY(-6px); }
    }

    @keyframes footerOverlayShift {
      0% { transform: translateX(0) translateY(0); }
      100% { transform: translateX(-140px) translateY(140px); }
    }

    @keyframes footerParticlesFloat {
      0% { transform: translateY(0); }
      100% { transform: translateY(-130px); }
    }

    /* Stats Section Background Effects */
    .stats-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 30% 70%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(147, 197, 253, 0.08) 0%, transparent 50%),
        linear-gradient(45deg, transparent 40%, rgba(59, 130, 246, 0.05) 50%, transparent 60%);
      opacity: 0.6;
      animation: statsPatternMove 18s ease-in-out infinite;
    }

    .stats-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(30deg, transparent 45%, rgba(96, 165, 250, 0.08) 50%, transparent 55%),
        linear-gradient(-30deg, transparent 45%, rgba(147, 197, 253, 0.06) 50%, transparent 55%);
      background-size: 80px 80px, 120px 120px;
      animation: statsOverlayShift 20s linear infinite;
    }

    .stats-glow {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(ellipse at 25% 25%, rgba(59, 130, 246, 0.15) 0%, transparent 40%),
        radial-gradient(ellipse at 75% 75%, rgba(96, 165, 250, 0.1) 0%, transparent 40%);
      animation: statsGlow 12s ease-in-out infinite;
    }

    @keyframes statsPatternMove {
      0%, 100% { transform: translateX(0) translateY(0) rotate(0deg); }
      25% { transform: translateX(10px) translateY(-5px) rotate(1deg); }
      50% { transform: translateX(-5px) translateY(10px) rotate(-1deg); }
      75% { transform: translateX(8px) translateY(5px) rotate(0.5deg); }
    }

    @keyframes statsOverlayShift {
      0% { transform: translateX(0) translateY(0); }
      100% { transform: translateX(80px) translateY(80px); }
    }

    @keyframes statsGlow {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 0.6; }
    }

    /* Transparent Royal Navigation */
    .modern-nav {
      background: transparent;
      backdrop-filter: none;
      border-bottom: none;
      box-shadow: none;
      transition: all 0.3s ease;
    }

    /* Scrolled state for navbar */
    .modern-nav.scrolled {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--glass-border);
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    
    /* Tech Cards */
    .tech-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(59, 130, 246, 0.1);
    }
    
    .tech-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.2);
    }

    /* Modern Buttons */
    .btn-primary {
      background: linear-gradient(135deg, #3b82f6, #1e40af);
      color: white;
      padding: 14px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      background: linear-gradient(135deg, #1e40af, #1e3a8a);
    }
    
    .btn-secondary {
      background: white;
      color: #1e40af;
      padding: 14px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: 2px solid #3b82f6;
    }
    
    .btn-secondary:hover {
      background: #3b82f6;
      color: white;
      transform: translateY(-2px);
    }

    /* Text Styles */
    .text-gradient {
      background: linear-gradient(135deg, #1e40af, #3b82f6, #60a5fa);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    /* Section Styles */
    .section-light {
      background: #f8fafc;
    }

    .section-white {
      background: white;
    }

    .section-stats {
      background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
      color: white;
      position: relative;
      overflow: hidden;
    }

    .section-blue {
      background:
        linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(59, 130, 246, 0.85) 50%, rgba(96, 165, 250, 0.8) 100%),
        url('https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2072&q=80') center/cover no-repeat;
      color: white;
      position: relative;
      overflow: hidden;
      background-attachment: fixed;
    }

    .section-footer {
      background:
        linear-gradient(135deg, rgba(30, 64, 175, 0.95) 0%, rgba(59, 130, 246, 0.9) 50%, rgba(96, 165, 250, 0.85) 100%),
        url('https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2072&q=80') center/cover no-repeat;
      color: white;
      position: relative;
      overflow: hidden;
      background-attachment: fixed;
    }
    
    /* Stats Counter */
    .stat-number {
      font-size: 3rem;
      font-weight: 800;
      color: #1e40af;
      line-height: 1;
    }

    .stat-number-dark {
      font-size: 3rem;
      font-weight: 800;
      background: linear-gradient(135deg, #60a5fa, #93c5fd, #dbeafe);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      line-height: 1;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .stat-description-dark {
      color: #cbd5e1;
      font-weight: 500;
    }
    
    /* Feature Icons */
    .feature-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #3b82f6, #60a5fa);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-bottom: 1rem;
    }

    /* Scroll Animation Effects */
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .fade-in.animate {
      opacity: 1;
      transform: translateY(0);
    }

    .slide-in-left {
      opacity: 0;
      transform: translateX(-50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-in-left.animate {
      opacity: 1;
      transform: translateX(0);
    }

    .slide-in-right {
      opacity: 0;
      transform: translateX(50px);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .slide-in-right.animate {
      opacity: 1;
      transform: translateX(0);
    }

    .scale-in {
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .scale-in.animate {
      opacity: 1;
      transform: scale(1);
    }

    .rotate-in {
      opacity: 0;
      transform: rotate(-10deg) scale(0.9);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .rotate-in.animate {
      opacity: 1;
      transform: rotate(0deg) scale(1);
    }

    .bounce-in {
      opacity: 0;
      transform: translateY(-30px);
      transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    .bounce-in.animate {
      opacity: 1;
      transform: translateY(0);
    }

    .flip-in {
      opacity: 0;
      transform: rotateY(-90deg);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .flip-in.animate {
      opacity: 1;
      transform: rotateY(0deg);
    }

    .zoom-in {
      opacity: 0;
      transform: scale(0.5);
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .zoom-in.animate {
      opacity: 1;
      transform: scale(1);
    }

    /* Staggered Animation Delays */
    .stagger-1 { transition-delay: 0.1s; }
    .stagger-2 { transition-delay: 0.2s; }
    .stagger-3 { transition-delay: 0.3s; }
    .stagger-4 { transition-delay: 0.4s; }
    .stagger-5 { transition-delay: 0.5s; }
    .stagger-6 { transition-delay: 0.6s; }

    /* Counter Animation */
    .counter {
      transition: all 0.8s ease-out;
    }

    /* Progress Bar Animation */
    .progress-bar {
      width: 0%;
      height: 4px;
      background: linear-gradient(90deg, #3b82f6, #60a5fa);
      border-radius: 2px;
      transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .progress-bar.animate {
      width: var(--progress-width);
    }

    /* Additional Background Effects */
    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(ellipse at top left, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at top right, rgba(147, 197, 253, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse at bottom left, rgba(96, 165, 250, 0.06) 0%, transparent 50%);
      z-index: 1;
      animation: backgroundPulse 15s ease-in-out infinite;
    }

    @keyframes backgroundPulse {
      0%, 100% { opacity: 0.3; }
      50% { opacity: 0.6; }
    }

    /* Enhanced text shadows for better readability */
    .hero-text-shadow {
      text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.2),
        0 8px 16px rgba(0, 0, 0, 0.1);
    }

    /* Hero content styling without glass effect */
    .hero-content {
      padding: 3rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero-section, .section-blue, .section-footer {
        padding: 2rem 1rem;
        background-attachment: scroll; /* Fix for mobile */
      }

      .hero-content {
        padding: 2rem;
      }

      .stat-number, .stat-number-dark {
        font-size: 2rem;
      }

      .tech-card {
        margin-bottom: 2rem;
      }

      /* Reduce animation intensity on mobile */
      .hero-particles, .services-particles, .footer-particles {
        opacity: 0.4;
      }

      .hero-overlay, .services-overlay, .footer-overlay {
        opacity: 0.6;
      }

      .hero-pattern, .services-pattern, .footer-pattern {
        opacity: 0.5;
      }
    }

    @media (max-width: 480px) {
      .hero-content {
        padding: 1.5rem;
      }

      /* Further reduce effects on very small screens */
      .services-particles, .footer-particles {
        opacity: 0.3;
      }
    }
  </style>
</head>
<body>
  <!-- Modern Tech Company Website -->
  
  <!-- Transparent Royal Navigation -->
  <nav class="modern-nav fixed w-full z-50 top-0">
    <div class="max-w-7xl mx-auto px-6 py-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-12 drop-shadow-lg">
          <span class="font-outfit font-bold text-2xl royal-gradient-text">Vishnorex</span>
        </div>
        <div class="hidden md:flex space-x-8">
          <a href="#home" class="nav-link">Home</a>
          <a href="#about" class="nav-link">About</a>
          <a href="#services" class="nav-link">Services</a>
          <a href="#portfolio" class="nav-link">Portfolio</a>
          <a href="#contact" class="nav-link">Contact</a>
        </div>
        <a href="#contact" class="btn-royal hidden md:inline-block">Get Started</a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero-section">
    <div class="hero-pattern"></div>
    <div class="hero-overlay"></div>
    <div class="hero-particles"></div>
    <div class="relative z-10 flex items-center justify-center min-h-screen px-6">
      <div class="text-center max-w-5xl mx-auto hero-content">
        <h1 class="text-5xl md:text-7xl font-space font-bold text-white mb-6 leading-tight hero-text-shadow">
          Custom Software Solutions to help Your Business
          <span class="text-blue-200">Succeed</span>
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 mb-10 leading-relaxed max-w-3xl mx-auto hero-text-shadow">
          We transform innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="#contact" class="btn-primary text-lg px-8 py-4 shadow-xl">Start Your Project</a>
          <a href="#about" class="btn-secondary text-lg px-8 py-4 shadow-xl">Learn More</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="section-stats py-20">
    <div class="stats-pattern"></div>
    <div class="stats-overlay"></div>
    <div class="stats-glow"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-6">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div class="bounce-in stagger-1">
          <div class="stat-number-dark counter" data-target="50">0</div>
          <p class="stat-description-dark font-medium">Projects Completed</p>
        </div>
        <div class="bounce-in stagger-2">
          <div class="stat-number-dark counter" data-target="25">0</div>
          <p class="stat-description-dark font-medium">Happy Clients</p>
        </div>
        <div class="bounce-in stagger-3">
          <div class="stat-number-dark counter" data-target="3">0</div>
          <p class="stat-description-dark font-medium">Years Experience</p>
        </div>
        <div class="bounce-in stagger-4">
          <div class="stat-number-dark">24/7</div>
          <p class="stat-description-dark font-medium">Support</p>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="section-light py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="grid md:grid-cols-2 gap-16 items-center">
        <div class="slide-in-left">
          <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">
            About Vishnorex Technologies
          </h2>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            We are a forward-thinking software development company specializing in custom solutions that drive business growth. Our team combines technical expertise with creative innovation to deliver exceptional digital experiences.
          </p>
          <div class="space-y-4">
            <div class="flex items-center space-x-3 fade-in stagger-1">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">Custom Software Development</span>
              <div class="progress-bar ml-auto" style="--progress-width: 95%;"></div>
            </div>
            <div class="flex items-center space-x-3 fade-in stagger-2">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">Mobile App Development</span>
              <div class="progress-bar ml-auto" style="--progress-width: 90%;"></div>
            </div>
            <div class="flex items-center space-x-3 fade-in stagger-3">
              <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm">✓</span>
              </div>
              <span class="text-gray-700">AI & Machine Learning Solutions</span>
              <div class="progress-bar ml-auto" style="--progress-width: 85%;"></div>
            </div>
          </div>
        </div>
        <div class="relative slide-in-right">
          <div class="tech-card p-8 scale-in">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">Our Mission</h3>
            <p class="text-gray-600 leading-relaxed">
              To empower businesses with innovative technology solutions that transform ideas into reality and drive sustainable growth in the digital age.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="section-blue py-24">
    <div class="services-pattern"></div>
    <div class="services-overlay"></div>
    <div class="services-particles"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-6">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-white mb-6 hero-text-shadow">Our Services</h2>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto hero-text-shadow">
          We offer comprehensive technology solutions to help your business thrive in the digital landscape.
        </p>
      </div>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20 flip-in stagger-1">
          <div class="feature-icon rotate-in">
            <span>🌐</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">Web Development</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Modern, responsive web applications built with React, Next.js, and cutting-edge frameworks for optimal performance.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-1">React</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-2">Next.js</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-3">TypeScript</span>
          </div>
        </div>
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20 flip-in stagger-2">
          <div class="feature-icon rotate-in">
            <span>📱</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">Mobile Apps</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Native iOS and Android applications with Flutter and React Native for seamless cross-platform experiences.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-1">Flutter</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-2">React Native</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-3">Swift</span>
          </div>
        </div>
        <div class="tech-card p-8 bg-white/10 backdrop-blur-sm border border-white/20 flip-in stagger-3">
          <div class="feature-icon rotate-in">
            <span>🤖</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">AI Solutions</h3>
          <p class="text-blue-100 mb-6 leading-relaxed">
            Intelligent solutions powered by machine learning, data analytics, and artificial intelligence technologies.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-1">TensorFlow</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-2">PyTorch</span>
            <span class="px-3 py-1 bg-white/20 text-white text-xs rounded-full fade-in stagger-3">OpenAI</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="section-light py-24">
    <div class="max-w-7xl mx-auto px-6">
      <div class="text-center mb-16 zoom-in">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">Featured Projects</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Discover some of our recent work and see how we've helped businesses transform their digital presence.
        </p>
      </div>
      <div class="grid md:grid-cols-2 gap-8">
        <div class="tech-card p-8 slide-in-left">
          <div class="text-5xl mb-6 bounce-in">🚀</div>
          <h3 class="text-2xl font-bold text-gray-800 mb-4 fade-in stagger-1">AI-Powered Travel Platform</h3>
          <p class="text-gray-600 mb-6 leading-relaxed fade-in stagger-2">
            Intelligent route optimization with machine learning algorithms, real-time booking, and personalized recommendations for enhanced user experience.
          </p>
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-1">React</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-2">Node.js</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-3">AI/ML</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-4">MongoDB</span>
          </div>
          <a href="#" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors fade-in stagger-3">View Project →</a>
        </div>
        <div class="tech-card p-8 slide-in-right">
          <div class="text-5xl mb-6 bounce-in">🏥</div>
          <h3 class="text-2xl font-bold text-gray-800 mb-4 fade-in stagger-1">Smart Healthcare Dashboard</h3>
          <p class="text-gray-600 mb-6 leading-relaxed fade-in stagger-2">
            Real-time patient monitoring with IoT integration, predictive analytics, and automated alert systems for improved healthcare delivery.
          </p>
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-1">Vue.js</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-2">Python</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-3">IoT</span>
            <span class="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full scale-in stagger-4">PostgreSQL</span>
          </div>
          <a href="#" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors fade-in stagger-3">View Project →</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="section-white py-24">
    <div class="max-w-4xl mx-auto px-6">
      <div class="text-center mb-16 fade-in">
        <h2 class="text-4xl md:text-5xl font-space font-bold text-gradient mb-6">Let's Build Something Amazing Together</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Ready to transform your ideas into reality? We'd love to hear about your project and discuss how we can help you succeed.
        </p>
      </div>
      <div class="tech-card p-12 scale-in">
        <form class="space-y-6">
          <div class="grid md:grid-cols-2 gap-6">
            <div class="slide-in-left stagger-1">
              <label class="block text-gray-700 font-medium mb-2">Your Name</label>
              <input type="text" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="John Doe" required>
            </div>
            <div class="slide-in-right stagger-1">
              <label class="block text-gray-700 font-medium mb-2">Email Address</label>
              <input type="email" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="<EMAIL>" required>
            </div>
          </div>
          <div class="fade-in stagger-2">
            <label class="block text-gray-700 font-medium mb-2">Project Type</label>
            <select class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
              <option>Web Development</option>
              <option>Mobile App Development</option>
              <option>AI & Machine Learning</option>
              <option>Custom Software Solution</option>
              <option>Other</option>
            </select>
          </div>
          <div class="fade-in stagger-3">
            <label class="block text-gray-700 font-medium mb-2">Project Details</label>
            <textarea rows="5" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none" placeholder="Tell us about your project, goals, and requirements..." required></textarea>
          </div>
          <div class="text-center bounce-in stagger-4">
            <button type="submit" class="btn-primary text-lg px-12 py-4">Send Message</button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="section-footer py-16">
    <div class="footer-pattern"></div>
    <div class="footer-overlay"></div>
    <div class="footer-particles"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-6">
      <div class="grid md:grid-cols-4 gap-8">
        <div class="md:col-span-2">
          <div class="flex items-center space-x-4 mb-6">
            <img src="./assests/nobg.png" alt="Vishnorex Technologies" class="h-10">
            <span class="font-space font-bold text-xl text-white hero-text-shadow">Vishnorex Technologies</span>
          </div>
          <p class="text-blue-100 leading-relaxed mb-6 hero-text-shadow">
            Transforming innovative ideas into powerful digital experiences with cutting-edge technology and human-centered design.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">📧</a>
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">📱</a>
            <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">🌐</a>
          </div>
        </div>
        <div>
          <h4 class="font-bold text-white mb-4 hero-text-shadow">Services</h4>
          <ul class="space-y-2 text-blue-100">
            <li><a href="#" class="hover:text-white transition-colors">Web Development</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Mobile Apps</a></li>
            <li><a href="#" class="hover:text-white transition-colors">AI Solutions</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Consulting</a></li>
          </ul>
        </div>
        <div>
          <h4 class="font-bold text-white mb-4 hero-text-shadow">Company</h4>
          <ul class="space-y-2 text-blue-100">
            <li><a href="#about" class="hover:text-white transition-colors">About Us</a></li>
            <li><a href="#portfolio" class="hover:text-white transition-colors">Portfolio</a></li>
            <li><a href="#contact" class="hover:text-white transition-colors">Contact</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-white/20 mt-12 pt-8 text-center">
        <p class="text-blue-100 hero-text-shadow">&copy; 2024 Vishnorex Technologies. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Scroll Animation JavaScript -->
  <script>
    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');

          // Handle counter animations
          if (entry.target.classList.contains('counter')) {
            animateCounter(entry.target);
          }

          // Handle progress bar animations
          if (entry.target.classList.contains('progress-bar')) {
            entry.target.classList.add('animate');
          }
        }
      });
    }, observerOptions);

    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in, .rotate-in, .bounce-in, .flip-in, .zoom-in, .counter, .progress-bar');

    animatedElements.forEach(el => {
      observer.observe(el);
    });

    // Counter animation function
    function animateCounter(element) {
      const target = parseInt(element.getAttribute('data-target'));
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;

      const updateCounter = () => {
        current += increment;
        if (current < target) {
          element.textContent = Math.floor(current) + '+';
          requestAnimationFrame(updateCounter);
        } else {
          element.textContent = target + '+';
        }
      };

      updateCounter();
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Enhanced parallax effects for all background sections
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;

      // Hero section parallax
      const heroPattern = document.querySelector('.hero-pattern');
      const heroOverlay = document.querySelector('.hero-overlay');
      const heroParticles = document.querySelector('.hero-particles');

      if (heroPattern) {
        heroPattern.style.transform = `translateY(${scrolled * 0.3}px)`;
      }
      if (heroOverlay) {
        heroOverlay.style.transform = `translateY(${scrolled * 0.2}px)`;
      }
      if (heroParticles) {
        heroParticles.style.transform = `translateY(${scrolled * 0.4}px)`;
      }

      // Stats section parallax
      const statsPattern = document.querySelector('.stats-pattern');
      const statsOverlay = document.querySelector('.stats-overlay');
      const statsGlow = document.querySelector('.stats-glow');

      if (statsPattern) {
        statsPattern.style.transform = `translateY(${(scrolled - window.innerHeight) * 0.2}px)`;
      }
      if (statsOverlay) {
        statsOverlay.style.transform = `translateY(${(scrolled - window.innerHeight) * 0.1}px)`;
      }
      if (statsGlow) {
        statsGlow.style.transform = `translateY(${(scrolled - window.innerHeight) * 0.15}px)`;
      }

      // Services section parallax
      const servicesPattern = document.querySelector('.services-pattern');
      const servicesOverlay = document.querySelector('.services-overlay');
      const servicesParticles = document.querySelector('.services-particles');

      if (servicesPattern) {
        servicesPattern.style.transform = `translateY(${(scrolled - window.innerHeight * 2) * 0.25}px)`;
      }
      if (servicesOverlay) {
        servicesOverlay.style.transform = `translateY(${(scrolled - window.innerHeight * 2) * 0.15}px)`;
      }
      if (servicesParticles) {
        servicesParticles.style.transform = `translateY(${(scrolled - window.innerHeight * 2) * 0.35}px)`;
      }

      // Footer section parallax
      const footerPattern = document.querySelector('.footer-pattern');
      const footerOverlay = document.querySelector('.footer-overlay');
      const footerParticles = document.querySelector('.footer-particles');

      if (footerPattern) {
        footerPattern.style.transform = `translateY(${(scrolled - window.innerHeight * 4) * 0.2}px)`;
      }
      if (footerOverlay) {
        footerOverlay.style.transform = `translateY(${(scrolled - window.innerHeight * 4) * 0.1}px)`;
      }
      if (footerParticles) {
        footerParticles.style.transform = `translateY(${(scrolled - window.innerHeight * 4) * 0.3}px)`;
      }
    });

    // Mouse movement parallax effect
    document.addEventListener('mousemove', (e) => {
      const heroSection = document.querySelector('.hero-section');
      if (!heroSection) return;

      const rect = heroSection.getBoundingClientRect();
      const x = (e.clientX - rect.left) / rect.width;
      const y = (e.clientY - rect.top) / rect.height;

      const heroPattern = document.querySelector('.hero-pattern');
      const heroOverlay = document.querySelector('.hero-overlay');

      if (heroPattern) {
        heroPattern.style.transform += ` translate(${x * 10}px, ${y * 10}px)`;
      }
      if (heroOverlay) {
        heroOverlay.style.transform += ` translate(${x * -5}px, ${y * -5}px)`;
      }
    });

    // Dynamic background color change based on scroll
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const heroSection = document.querySelector('.hero-section');

      if (heroSection && scrolled < window.innerHeight) {
        const opacity = Math.max(0.75, 1 - scrolled / window.innerHeight);
        heroSection.style.setProperty('--bg-opacity', opacity);
      }
    });

    // Form submission handler
    document.querySelector('form').addEventListener('submit', function(e) {
      e.preventDefault();

      // Add loading state to button
      const button = this.querySelector('button[type="submit"]');
      const originalText = button.textContent;
      button.textContent = 'Sending...';
      button.disabled = true;

      // Simulate form submission
      setTimeout(() => {
        alert('Thank you for your message! We\'ll get back to you soon.');
        this.reset();
        button.textContent = originalText;
        button.disabled = false;
      }, 2000);
    });

    // Add loading animation to page
    window.addEventListener('load', () => {
      document.body.style.opacity = '0';
      document.body.style.transition = 'opacity 0.5s ease-in-out';

      setTimeout(() => {
        document.body.style.opacity = '1';
      }, 100);
    });
  </script>

</body>
</html>
